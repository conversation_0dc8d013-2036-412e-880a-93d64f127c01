/**
 * Sync metadata storage service using Obsidian's plugin data storage
 * This stores internal sync timestamps separately from frontmatter
 */

import type { Plugin, TFile } from 'obsidian';

export interface SyncMetadata {
  changed_at?: string;
  synced_at?: string;
}

export interface SyncMetadataStore {
  [filePath: string]: SyncMetadata;
}

export class SyncMetadataStorage {
  private plugin: Plugin;
  private metadata: SyncMetadataStore = {};
  private readonly STORAGE_KEY = 'sync-metadata';

  constructor(plugin: Plugin) {
    this.plugin = plugin;
  }

  /**
   * Load metadata from plugin storage
   */
  async load(): Promise<void> {
    try {
      const data = await this.plugin.loadData();
      this.metadata = data?.[this.STORAGE_KEY] || {};
    } catch (error) {
      console.error('Error loading sync metadata:', error);
      this.metadata = {};
    }
  }

  /**
   * Save metadata to plugin storage
   */
  async save(): Promise<void> {
    try {
      const data = await this.plugin.loadData() || {};
      data[this.STORAGE_KEY] = this.metadata;
      await this.plugin.saveData(data);
    } catch (error) {
      console.error('Error saving sync metadata:', error);
    }
  }

  /**
   * Set the changed_at timestamp for a file
   */
  async setChangedAt(file: TFile, timestamp: string): Promise<void> {
    const filePath = file.path;
    if (!this.metadata[filePath]) {
      this.metadata[filePath] = {};
    }
    this.metadata[filePath].changed_at = timestamp;
    await this.save();
  }

  /**
   * Set the synced_at timestamp for a file
   */
  async setSyncedAt(file: TFile, timestamp: string): Promise<void> {
    const filePath = file.path;
    if (!this.metadata[filePath]) {
      this.metadata[filePath] = {};
    }
    this.metadata[filePath].synced_at = timestamp;
    await this.save();
  }

  /**
   * Get sync metadata for a file
   */
  getMetadata(file: TFile): SyncMetadata {
    return this.metadata[file.path] || {};
  }

  /**
   * Get changed_at timestamp for a file
   */
  getChangedAt(file: TFile): string | undefined {
    return this.metadata[file.path]?.changed_at;
  }

  /**
   * Get synced_at timestamp for a file
   */
  getSyncedAt(file: TFile): string | undefined {
    return this.metadata[file.path]?.synced_at;
  }

  /**
   * Clear metadata for a file (when file is deleted)
   */
  async clearMetadata(file: TFile): Promise<void> {
    delete this.metadata[file.path];
    await this.save();
  }

  /**
   * Update changed_at when file content changes
   */
  async markAsChanged(file: TFile): Promise<void> {
    await this.setChangedAt(file, new Date().toISOString());
  }

  /**
   * Mark file as synced
   */
  async markAsSynced(file: TFile): Promise<void> {
    await this.setSyncedAt(file, new Date().toISOString());
  }

  /**
   * Set both timestamps when syncing from Ghost
   */
  async setSyncFromGhost(file: TFile, ghostUpdatedAt: string): Promise<void> {
    const filePath = file.path;
    const syncTime = new Date().toISOString();

    if (!this.metadata[filePath]) {
      this.metadata[filePath] = {};
    }

    this.metadata[filePath].changed_at = ghostUpdatedAt;
    this.metadata[filePath].synced_at = syncTime;

    await this.save();
  }
}
