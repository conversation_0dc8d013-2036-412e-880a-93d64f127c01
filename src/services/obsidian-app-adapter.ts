import type { App, TFile } from "obsidian";
import type { AppAdapter, FileMetadata } from "./sync-status-service";

export class ObsidianAppAdapter implements AppAdapter {
  private app: App;

  constructor(app: App) {
    this.app = app;
  }

  async readFile(file: TFile): Promise<string> {
    return await this.app.vault.read(file);
  }

  getFileMetadata(file: TFile): FileMetadata | null {
    try {
      const cache = this.app.metadataCache.getFileCache(file);
      return {
        frontmatter: cache?.frontmatter,
        content: '' // We'll read content separately when needed
      };
    } catch (error) {
      console.error('Error getting file metadata:', error);
      return null;
    }
  }

  /**
   * Get file metadata with retry logic for cases where metadata cache is updating
   */
  async getFileMetadataWithRetry(file: TFile, maxRetries: number = 3, delay: number = 100): Promise<FileMetadata | null> {
    for (let i = 0; i < maxRetries; i++) {
      const metadata = this.getFileMetadata(file);

      if (metadata?.frontmatter) {
        return metadata;
      }

      if (i < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    return this.getFileMetadata(file);
  }
}
