<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import type { GhostPost, GhostNewsletter } from '../types';
  import type { PublishOptions } from './types';
  import { getPluginContext } from './context';

  export let ghostPost: GhostPost;
  export let show: boolean = false;

  const { plugin } = getPluginContext();
  const dispatch = createEventDispatcher<{
    confirm: PublishOptions;
    cancel: void;
  }>();

  let selectedAction: 'publish' | 'send-only' | 'publish-send' = 'publish-send';
  let selectedNewsletter: string = '';
  let emailSegment: string = '';
  let testMode: boolean = false;

  $: emailAlreadySent = ghostPost.email !== null;
  $: {
    if (emailAlreadySent) {
      selectedAction = 'publish';
    }
  }

  $: actionOptions = [
    {
      value: 'publish',
      label: 'Publish to website only',
      desc: 'Make the post live on your website',
      disabled: false
    },
    {
      value: 'send-only',
      label: 'Send email only',
      desc: 'Send newsletter without publishing to website',
      disabled: emailAlreadySent
    },
    {
      value: 'publish-send',
      label: 'Publish & send newsletter',
      desc: 'Publish to website and send newsletter',
      disabled: emailAlreadySent
    }
  ];

  function handleConfirm() {
    const options: PublishOptions = {
      action: selectedAction,
      newsletter: plugin.newsletters.find(n => n.slug === selectedNewsletter),
      emailSegment,
      testMode
    };
    dispatch('confirm', options);
    show = false;
  }

  function handleCancel() {
    dispatch('cancel');
    show = false;
  }

  function handleBackdropClick(event: MouseEvent) {
    if (event.target === event.currentTarget) {
      handleCancel();
    }
  }

  function handleBackdropKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape' || event.key === 'Enter') {
      dispatch('cancel');
    }
  }
</script>

{#if show}
  <div class="modal-backdrop" role="button" tabindex="0" on:click={handleBackdropClick} on:keydown={handleBackdropKeydown}>
    <div class="modal-content ghost-publish-dialog">
      <h2>
        {emailAlreadySent ? 'Publish Options (Email Already Sent)' : 'Publish Options'}
      </h2>

      <!-- Action Selection -->
      <div class="publish-dialog-section">
        <h3>Action</h3>

        {#each actionOptions as option}
          <div class="publish-dialog-option {option.disabled ? 'publish-dialog-option-disabled' : ''}">
            <label>
              <input
                type="radio"
                bind:group={selectedAction}
                value={option.value}
                disabled={option.disabled}
                class="publish-dialog-radio"
              />
              <div class="publish-dialog-label">
                <strong>{option.label}</strong>
                <div class="publish-dialog-desc {option.disabled ? 'publish-dialog-desc-disabled' : ''}">
                  {option.desc}
                  {#if option.disabled && emailAlreadySent}
                    (Email already sent)
                  {/if}
                </div>
              </div>
            </label>
          </div>
        {/each}
      </div>

      <!-- Newsletter Selection -->
      {#if selectedAction !== 'publish' && plugin.newsletters.length > 0}
        <div class="publish-dialog-section">
          <h3>Newsletter</h3>
          <select bind:value={selectedNewsletter} class="publish-dialog-select">
            <option value="">Select newsletter...</option>
            {#each plugin.newsletters as newsletter}
              <option value={newsletter.slug}>{newsletter.name}</option>
            {/each}
          </select>
        </div>

        <!-- Email Segment -->
        <div class="publish-dialog-section">
          <h3>Email Segment (optional)</h3>
          <input
            type="text"
            bind:value={emailSegment}
            placeholder="e.g., status:free, status:-free"
            class="publish-dialog-input"
          />
          <div class="publish-dialog-desc">
            Filter recipients by member status or other criteria
          </div>
        </div>

        <!-- Test Mode -->
        <div class="publish-dialog-section">
          <label class="publish-dialog-checkbox-label">
            <input
              type="checkbox"
              bind:checked={testMode}
              class="publish-dialog-checkbox"
            />
            Test mode (send to 'tester' members only)
          </label>
        </div>
      {/if}

      <!-- Buttons -->
      <div class="publish-dialog-buttons">
        <button class="ghost-sync-btn" on:click={handleCancel}>
          Cancel
        </button>
        <button
          class="ghost-sync-btn mod-cta"
          on:click={handleConfirm}
          disabled={selectedAction !== 'publish' && !selectedNewsletter}
        >
          Confirm
        </button>
      </div>
    </div>
  </div>
{/if}

<style>
  .modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .modal-content {
    background: var(--background-primary);
    border-radius: 8px;
    padding: 24px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    border: 1px solid var(--background-modifier-border);
  }

  .publish-dialog-section {
    margin-bottom: 20px;
  }

  .publish-dialog-option {
    margin-bottom: 12px;
  }

  .publish-dialog-option label {
    display: flex;
    align-items: flex-start;
    cursor: pointer;
  }

  .publish-dialog-option-disabled label {
    cursor: not-allowed;
    opacity: 0.6;
  }

  .publish-dialog-radio {
    margin-right: 8px;
    margin-top: 2px;
  }

  .publish-dialog-label {
    flex: 1;
  }

  .publish-dialog-desc {
    font-size: 12px;
    color: var(--text-muted);
    margin-top: 4px;
  }

  .publish-dialog-desc-disabled {
    color: var(--text-faint);
  }

  .publish-dialog-select,
  .publish-dialog-input {
    width: 100%;
    padding: 8px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    background: var(--background-primary);
    color: var(--text-normal);
  }

  .publish-dialog-checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
  }

  .publish-dialog-checkbox {
    margin-right: 8px;
  }

  .publish-dialog-buttons {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
  }
</style>
