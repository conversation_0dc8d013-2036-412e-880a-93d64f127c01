#!/usr/bin/env node

/**
 * Ghost API Recordings Management Script
 * 
 * This script helps manage HAR files for Ghost API mocking in e2e tests.
 * 
 * Usage:
 *   node scripts/manage-ghost-api-recordings.js list
 *   node scripts/manage-ghost-api-recordings.js clean
 *   node scripts/manage-ghost-api-recordings.js info <scenario>
 */

const fs = require('fs');
const path = require('path');

const HARS_DIR = path.join(__dirname, '../e2e/hars');

function listRecordings() {
  console.log('📁 Ghost API Recordings:');
  console.log('========================');
  
  if (!fs.existsSync(HARS_DIR)) {
    console.log('No recordings directory found. Run some tests with GHOST_API_RECORD=true first.');
    return;
  }

  const files = fs.readdirSync(HARS_DIR)
    .filter(file => file.endsWith('.har'))
    .sort();

  if (files.length === 0) {
    console.log('No HAR files found. Run some tests with GHOST_API_RECORD=true to create recordings.');
    return;
  }

  files.forEach(file => {
    const filePath = path.join(HARS_DIR, file);
    const stats = fs.statSync(filePath);
    const sizeKB = Math.round(stats.size / 1024);
    const scenario = file.replace('ghost-api-', '').replace('.har', '');
    
    console.log(`  📄 ${scenario}`);
    console.log(`     File: ${file}`);
    console.log(`     Size: ${sizeKB} KB`);
    console.log(`     Modified: ${stats.mtime.toISOString()}`);
    console.log('');
  });
}

function cleanRecordings() {
  console.log('🧹 Cleaning Ghost API Recordings...');
  
  if (!fs.existsSync(HARS_DIR)) {
    console.log('No recordings directory found.');
    return;
  }

  const files = fs.readdirSync(HARS_DIR)
    .filter(file => file.endsWith('.har'));

  if (files.length === 0) {
    console.log('No HAR files to clean.');
    return;
  }

  console.log(`Found ${files.length} HAR files to delete:`);
  files.forEach(file => {
    console.log(`  - ${file}`);
  });

  // In a real script, you might want to add confirmation here
  console.log('\nTo delete these files, run:');
  files.forEach(file => {
    console.log(`  rm "${path.join(HARS_DIR, file)}"`);
  });
}

function showRecordingInfo(scenario) {
  const fileName = `ghost-api-${scenario}.har`;
  const filePath = path.join(HARS_DIR, fileName);

  console.log(`📊 Recording Info: ${scenario}`);
  console.log('================================');

  if (!fs.existsSync(filePath)) {
    console.log(`❌ Recording not found: ${fileName}`);
    console.log('Available recordings:');
    listRecordings();
    return;
  }

  try {
    const harContent = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    const entries = harContent.log?.entries || [];
    
    console.log(`📄 File: ${fileName}`);
    console.log(`📊 Total requests: ${entries.length}`);
    
    if (entries.length > 0) {
      console.log('\n🌐 Recorded requests:');
      entries.forEach((entry, index) => {
        const url = entry.request?.url || 'Unknown URL';
        const method = entry.request?.method || 'Unknown';
        const status = entry.response?.status || 'Unknown';
        
        console.log(`  ${index + 1}. ${method} ${url} → ${status}`);
      });

      // Show unique domains
      const domains = [...new Set(entries.map(entry => {
        try {
          return new URL(entry.request?.url || '').hostname;
        } catch {
          return 'unknown';
        }
      }))];

      console.log('\n🏠 Domains:');
      domains.forEach(domain => {
        console.log(`  - ${domain}`);
      });
    }

    const stats = fs.statSync(filePath);
    console.log(`\n📏 File size: ${Math.round(stats.size / 1024)} KB`);
    console.log(`📅 Last modified: ${stats.mtime.toISOString()}`);

  } catch (error) {
    console.error(`❌ Error reading HAR file: ${error.message}`);
  }
}

function showHelp() {
  console.log('Ghost API Recordings Management');
  console.log('==============================');
  console.log('');
  console.log('Commands:');
  console.log('  list                    List all recordings');
  console.log('  clean                   Show commands to clean recordings');
  console.log('  info <scenario>         Show detailed info about a recording');
  console.log('  help                    Show this help');
  console.log('');
  console.log('Examples:');
  console.log('  node scripts/manage-ghost-api-recordings.js list');
  console.log('  node scripts/manage-ghost-api-recordings.js info create-new-post');
  console.log('');
  console.log('To record new interactions:');
  console.log('  GHOST_API_RECORD=true npm run test:e2e');
  console.log('');
  console.log('To run tests with mocked responses:');
  console.log('  npm run test:e2e');
}

// Main execution
const command = process.argv[2];
const scenario = process.argv[3];

switch (command) {
  case 'list':
    listRecordings();
    break;
  case 'clean':
    cleanRecordings();
    break;
  case 'info':
    if (!scenario) {
      console.error('❌ Please provide a scenario name');
      console.log('Usage: node scripts/manage-ghost-api-recordings.js info <scenario>');
      process.exit(1);
    }
    showRecordingInfo(scenario);
    break;
  case 'help':
  case '--help':
  case '-h':
    showHelp();
    break;
  default:
    console.error('❌ Unknown command:', command);
    showHelp();
    process.exit(1);
}
