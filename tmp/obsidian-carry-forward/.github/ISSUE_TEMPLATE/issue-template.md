---
name: Issue Template
about: Use this template to create a new issue.
title: ''
labels: ''
assignees: ''

---

Issue tracker is **ONLY** used for reporting bugs. New feature ideas should be discussed in the [ideas section](https://github.com/jglev/obsidian-apply-patterns-plugin/discussions/categories/ideas). Please use the [Q&A section](https://github.com/jglev/obsidian-apply-patterns-plugin/discussions/categories/q-a) for supporting issues. Please use the search function.

If you encountered the issue after you installed, updated, or reloaded the Carry Forward plugin, please try restarting obsidian before reporting the bug.

If you want to report a bug, please follow the guide lines below to help me resolve it.

## Expected Behavior
<!--- Tell us what should happen -->

## Current Behavior
<!--- Tell us what happens instead of the expected behavior -->

## Steps to Reproduce
<!-- Which exact steps can I take to reproduce the issue? -->

## Context (Environment)
* Obsidian version:
* Carry Forward version:
* [ ] I have tried it with all other plugins disabled and the error still occurs

## Possible Solution
<!--- Not obligatory, but suggest a fix/reason for the bug, if you have an idea -->
