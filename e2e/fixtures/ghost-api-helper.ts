import type { Page, BrowserContext } from 'playwright';
import * as path from 'path';

/**
 * Ghost API Helper for E2E Tests
 *
 * This helper provides utilities for mocking Ghost API requests using <PERSON><PERSON>'s
 * HAR (HTTP Archive) recording and replaying functionality. This is the TypeScript
 * equivalent of <PERSON>'s VCR gem.
 */

export interface GhostAPIHelperOptions {
  /** Base URL for Ghost API (e.g., 'https://your-ghost-site.com') */
  ghostUrl: string;
  /** Whether to record new interactions (true) or replay existing ones (false) */
  record?: boolean;
  /** Name of the test scenario for HAR file naming */
  scenario: string;
}

export class GhostAPIHelper {
  private page: Page;
  private context: BrowserContext;
  private options: GhostAPIHelperOptions;
  private harPath: string;

  constructor(page: Page, context: BrowserContext, options: GhostAPIHelperOptions) {
    this.page = page;
    this.context = context;
    this.options = options;

    // Use a more reliable path resolution
    const currentDir = process.cwd();
    this.harPath = path.join(currentDir, 'e2e/hars', `ghost-api-${options.scenario}.har`);
  }

  /**
   * Set up Ghost API mocking for the test
   *
   * @param record - Whether to record new interactions (default: false)
   */
  async setupGhostAPIMocking(record: boolean = false): Promise<void> {
    const ghostApiPattern = `${this.options.ghostUrl}/ghost/api/**`;

    console.log(`Setting up Ghost API mocking for scenario: ${this.options.scenario}`);
    console.log(`HAR file: ${this.harPath}`);
    console.log(`Recording mode: ${record}`);
    console.log(`API pattern: ${ghostApiPattern}`);

    // Check if HAR file exists when not recording
    if (!record) {
      const fs = await import('fs');
      const path = await import('path');

      // Ensure hars directory exists
      const harsDir = path.dirname(this.harPath);
      if (!fs.existsSync(harsDir)) {
        fs.mkdirSync(harsDir, { recursive: true });
        console.log(`📁 Created hars directory: ${harsDir}`);
      }

      if (!fs.existsSync(this.harPath)) {
        console.log(`⚠️  HAR file not found: ${this.harPath}`);
        console.log(`🔄 Switching to recording mode to create HAR file`);
        record = true;
      }
    }

    await this.page.routeFromHAR(this.harPath, {
      url: ghostApiPattern,
      update: record,
      // Only match Ghost API requests
      notFound: record ? 'fallback' : 'abort'
    });

    if (record) {
      console.log(`📹 Recording Ghost API interactions to: ${this.harPath}`);
    } else {
      console.log(`🎬 Replaying Ghost API interactions from: ${this.harPath}`);
    }
  }

  /**
   * Record Ghost API interactions for a specific test scenario
   * Use this when you need to capture new API responses
   */
  async recordGhostAPIInteractions(): Promise<void> {
    await this.setupGhostAPIMocking(true);
  }

  /**
   * Replay Ghost API interactions from recorded HAR file
   * Use this for normal test execution
   */
  async replayGhostAPIInteractions(): Promise<void> {
    await this.setupGhostAPIMocking(false);
  }

  /**
   * Create a new Ghost API helper instance for a specific test scenario
   */
  static create(page: Page, context: BrowserContext, options: GhostAPIHelperOptions): GhostAPIHelper {
    return new GhostAPIHelper(page, context, options);
  }

  /**
   * Get the path to the HAR file for this scenario
   */
  getHarPath(): string {
    return this.harPath;
  }
}

/**
 * Utility function to set up Ghost API mocking in tests
 *
 * @example
 * ```typescript
 * // In your test file
 * beforeEach(async () => {
 *   await setupGhostAPIMocking(page, context, {
 *     ghostUrl: 'https://demo.ghost.io',
 *     scenario: 'create-post',
 *     record: false // Set to true when recording new interactions
 *   });
 * });
 * ```
 */
export async function setupGhostAPIMocking(
  page: Page,
  context: BrowserContext,
  options: GhostAPIHelperOptions
): Promise<GhostAPIHelper> {
  const helper = GhostAPIHelper.create(page, context, options);

  if (options.record) {
    await helper.recordGhostAPIInteractions();
  } else {
    await helper.replayGhostAPIInteractions();
  }

  return helper;
}

/**
 * Environment variable to control recording mode
 * Set GHOST_API_RECORD=true to record new interactions
 */
export function shouldRecordGhostAPI(): boolean {
  return process.env.GHOST_API_RECORD === 'true';
}
