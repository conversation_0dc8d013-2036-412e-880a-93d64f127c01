import { afterEach } from 'vitest';
import { globalUIReset } from './helpers/test-setup';

/**
 * Global setup for all e2e tests
 * This ensures UI reset happens after EVERY test across ALL test suites
 * preventing modal pollution between tests
 */

// Apply global UI reset after each test
afterEach(async () => {
  await globalUIReset();
});

console.log('🔧 Global e2e test setup loaded - UI reset will be applied after every test');
