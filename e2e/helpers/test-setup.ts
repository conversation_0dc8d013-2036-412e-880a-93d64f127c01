import type { Page } from 'playwright';
import { afterEach, beforeEach } from 'vitest';
import { resetObsidianUI } from './plugin-setup';

/**
 * Global test setup utilities for e2e tests
 * Provides consistent UI state management across all test suites
 */

// Global page reference for UI reset
let globalPage: Page | null = null;

/**
 * Register the global page for UI reset
 * This should be called in beforeAll of each test suite
 */
export function registerPageForUIReset(page: Page): void {
  globalPage = page;
}

/**
 * Setup afterEach hook for UI reset
 * Call this in any test suite that opens modals or changes UI state
 */
export function setupUIReset(page: Page): void {
  afterEach(async () => {
    // Reset UI state after each test to prevent modal pollution
    await resetObsidianUI(page);
  });
}

/**
 * Setup comprehensive test environment with UI reset
 * Includes both plugin verification and UI reset
 */
export async function setupTestEnvironment(page: Page): Promise<void> {
  const { verifyPluginAvailable } = await import('./plugin-setup');

  // Register page for global UI reset
  registerPageForUIReset(page);

  // Verify plugin is available (once for all tests)
  await verifyPluginAvailable(page);

  // Setup UI reset after each test
  setupUIReset(page);
}

/**
 * Global UI reset that runs after every test in any e2e suite
 * This is automatically applied via vitest setup
 */
export async function globalUIReset(): Promise<void> {
  if (globalPage) {
    try {
      await resetObsidianUI(globalPage);
    } catch (error) {
      console.log(`⚠️ Global UI reset failed: ${error.message}`);
    }
  }
}
