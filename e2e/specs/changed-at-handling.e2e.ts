import { chromium } from 'playwright';
import type { <PERSON><PERSON><PERSON>, <PERSON> } from 'playwright';
import { expect, test, describe, beforeAll, afterAll, beforeEach } from 'vitest';
import { setupTestEnvironment } from '../helpers/test-setup';
import { resetVault } from '../utils/vault-utils.js';

/**
 * Wait for async operations to complete with smart polling
 */
async function waitForAsyncOperation(timeout: number = 1000): Promise<void> {
  await new Promise(resolve => setTimeout(resolve, timeout));
}

/**
 * Wait for a file to exist in Obsidian's vault
 */
async function waitForFileToExist(page: Page, filePath: string, timeout: number = 5000): Promise<boolean> {
  try {
    await page.waitForFunction(
      ({ path }) => {
        const file = (window as any).app.vault.getAbstractFileByPath(path);
        return !!file;
      },
      { path: filePath },
      { timeout }
    );
    return true;
  } catch (error) {
    console.log(`File ${filePath} did not appear within ${timeout}ms`);
    return false;
  }
}



/**
 * Helper to access plugin sync metadata via Obsidian
 * Enhanced with better error handling and file resolution
 */
async function getSyncMetadata(page: Page, filePath: string): Promise<any> {
  // First, wait a bit to ensure file operations have completed
  await waitForAsyncOperation(500);

  return await page.evaluate(({ path }) => {
    const plugin = (window as any).app.plugins.plugins['ghost-sync'];
    if (!plugin || !plugin.syncMetadata) {
      throw new Error('❌ Ghost sync plugin or syncMetadata not found');
    }

    // Get the TFile object - try multiple path variations
    let file = (window as any).app.vault.getAbstractFileByPath(path);
    if (!file && !path.startsWith('/')) {
      // Try with leading slash
      file = (window as any).app.vault.getAbstractFileByPath('/' + path);
    }
    if (!file && path.startsWith('/')) {
      // Try without leading slash
      file = (window as any).app.vault.getAbstractFileByPath(path.substring(1));
    }

    if (!file) {
      // List available files for debugging with more detail
      const allFiles = (window as any).app.vault.getAllLoadedFiles()
        .filter((f: any) => f.path.endsWith('.md'))
        .map((f: any) => f.path);

      console.log(`🔍 File not found: ${path}`);
      console.log(`🔍 Available files: ${allFiles.join(', ')}`);
      console.log(`🔍 Total files in vault: ${allFiles.length}`);

      throw new Error(`❌ File not found: ${path}. Available files: ${allFiles.join(', ')}`);
    }

    console.log(`✅ Found file: ${file.path}`);
    const metadata = plugin.syncMetadata.getMetadata(file);
    return metadata || {}; // Return empty object if no metadata exists yet
  }, { path: filePath });
}

/**
 * Helper to get changed_at timestamp for a file
 */
async function getChangedAt(page: Page, filePath: string): Promise<string | null> {
  try {
    const metadata = await getSyncMetadata(page, filePath);
    return metadata?.changed_at || null;
  } catch (error) {
    // If file doesn't exist or has no metadata yet, return null
    console.log(`No metadata found for ${filePath}: ${error.message}`);
    return null;
  }
}



/**
 * Helper to create a test file with specific content using Obsidian's vault API
 * Enhanced with better error handling and verification
 */
async function createTestFile(page: Page, filePath: string, content: string): Promise<void> {
  console.log(`📝 Creating test file: ${filePath}`);

  // Retry file creation up to 5 times with exponential backoff
  for (let attempt = 1; attempt <= 5; attempt++) {
    const result = await page.evaluate(async ({ path, fileContent, attemptNum }) => {
      try {
        // Ensure the articles directory exists
        const articlesDir = 'articles';
        const articlesFolder = (window as any).app.vault.getAbstractFileByPath(articlesDir);
        if (!articlesFolder) {
          try {
            await (window as any).app.vault.createFolder(articlesDir);
            console.log(`📁 Created articles directory`);
          } catch (e) {
            // Folder might already exist, ignore error
            console.log(`📁 Articles directory already exists`);
          }
        }

        // Check if file already exists and delete it first
        const existingFile = (window as any).app.vault.getAbstractFileByPath(path);
        if (existingFile) {
          await (window as any).app.vault.delete(existingFile);
          console.log(`🗑️  Deleted existing file: ${path}`);
          // Wait a bit for deletion to complete
          await new Promise(resolve => setTimeout(resolve, 200));
        }

        // Create the file using Obsidian's vault API
        await (window as any).app.vault.create(path, fileContent);
        return { success: true, message: `✅ Created file: ${path} (attempt ${attemptNum})` };
      } catch (error) {
        return { success: false, error: error.message, attempt: attemptNum };
      }
    }, { path: filePath, fileContent: content, attemptNum: attempt });

    if (result.success) {
      console.log(result.message);

      // Wait for the file to be available in the vault with longer timeout
      const fileExists = await waitForFileToExist(page, filePath, 10000);
      if (fileExists) {
        console.log(`✅ File verified to exist: ${filePath}`);
        return; // Success!
      }
    }

    console.log(`❌ File creation attempt ${attempt} failed: ${result.error || 'File not found after creation'}`);

    if (attempt < 5) {
      // Exponential backoff: 500ms, 1s, 2s, 4s
      const waitTime = 500 * Math.pow(2, attempt - 1);
      await waitForAsyncOperation(waitTime);
    }
  }

  // If all attempts failed, list available files for debugging
  const availableFiles = await page.evaluate(() => {
    const allFiles = (window as any).app.vault.getAllLoadedFiles()
      .filter((f: any) => f.path.endsWith('.md'))
      .map((f: any) => f.path);
    return allFiles;
  });

  throw new Error(`❌ Failed to create test file ${filePath} after 5 attempts. Available files: ${availableFiles.join(', ')}`);
}

describe("Ghost Sync - changed_at Handling E2E Tests", () => {
  let browser: Browser;
  let page: Page;

  beforeAll(async () => {
    // Connect to existing Obsidian instance via CDP
    browser = await chromium.connectOverCDP('http://127.0.0.1:9222');

    // Get the first page (Obsidian window)
    const contexts = browser.contexts();
    const context = contexts[0];
    const pages = context.pages();
    page = pages[0];

    console.log("Connected to Obsidian via Playwright");

    // Enable the ghost-sync plugin
    await page.evaluate(() => {
      (window as any).app.plugins.setEnable(true);
      (window as any).app.plugins.enablePlugin('ghost-sync');
    });

    // Wait for plugin to be ready
    await waitForAsyncOperation(1000);

    // Setup test environment with plugin verification and UI reset
    await setupTestEnvironment(page);
  });

  afterAll(async () => {
    if (browser) {
      await browser.close();
    }
  });

  beforeEach(async () => {
    // Restore vault from pristine state before each test
    await resetVault();
    await waitForAsyncOperation(500);

    // Reload the plugin to pick up the restored settings
    await page.evaluate(async () => {
      const app = (window as any).app;
      await app.plugins.disablePlugin('ghost-sync');
      await app.plugins.enablePlugin('ghost-sync');
    });
    await waitForAsyncOperation(500);
  });

  test("should set changed_at when manually marking file as changed", async () => {
    const testTitle = "Test Changed At Post";
    const testSlug = "test-changed-at-post";
    const relativeFilePath = `articles/${testSlug}.md`;

    // Create a test file with frontmatter including a slug
    const content = `---
Title: "${testTitle}"
Slug: "${testSlug}"
Status: "draft"
Newsletter: null
---

# ${testTitle}

This is test content for changed_at handling.`;

    await createTestFile(page, relativeFilePath, content);

    // Give a moment for the file to be fully processed by Obsidian
    await waitForAsyncOperation(500);

    // Verify no initial changed_at (should be null for new files)
    const initialChangedAt = await getChangedAt(page, relativeFilePath);
    console.log(`Initial changed_at: ${initialChangedAt}`);
    expect(initialChangedAt).toBeNull();

    // Manually mark the file as changed using the plugin's sync metadata
    const markResult = await page.evaluate(async (path) => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      if (!plugin || !plugin.syncMetadata) {
        return { success: false, error: 'Plugin or syncMetadata not found' };
      }

      // Get the TFile object - try both with and without leading slash
      let file = (window as any).app.vault.getAbstractFileByPath(path);
      if (!file && !path.startsWith('/')) {
        file = (window as any).app.vault.getAbstractFileByPath('/' + path);
      }
      if (!file && path.startsWith('/')) {
        file = (window as any).app.vault.getAbstractFileByPath(path.substring(1));
      }

      if (!file) {
        const allFiles = (window as any).app.vault.getAllLoadedFiles()
          .filter((f: any) => f.path.endsWith('.md'))
          .map((f: any) => f.path);
        return { success: false, error: `File not found: ${path}. Available: ${allFiles.join(', ')}` };
      }

      try {
        await plugin.syncMetadata.markAsChanged(file);
        return { success: true, filePath: file.path };
      } catch (error) {
        return { success: false, error: error.message };
      }
    }, relativeFilePath);

    console.log('Mark as changed result:', JSON.stringify(markResult, null, 2));
    if (!markResult.success) {
      console.error('❌ markAsChanged failed:', markResult.error || 'Unknown error');
      console.error('Full markResult:', markResult);
    }
    expect(markResult.success).toBe(true);

    await waitForAsyncOperation(1000);

    // Check that changed_at was set
    const changedAt = await getChangedAt(page, relativeFilePath);

    expect(changedAt).toBeTruthy();
    expect(new Date(changedAt).getTime()).toBeGreaterThan(0);

    // If there was an initial timestamp, verify the new one is different
    if (initialChangedAt) {
      expect(changedAt).not.toBe(initialChangedAt);
    }

    console.log(`✅ changed_at set for file: ${changedAt}`);
  });

  test("should update changed_at timestamp when marked multiple times", async () => {
    const testSlug = "test-multiple-changes";
    const relativeFilePath = `articles/${testSlug}.md`;

    // Create initial file
    const initialContent = `---
Title: "Test Multiple Changes"
Slug: "${testSlug}"
Status: "draft"
---

Initial content.`;

    await createTestFile(page, relativeFilePath, initialContent);

    // Give a moment for the file to be fully processed by Obsidian
    await waitForAsyncOperation(500);

    // Mark as changed first time
    await page.evaluate((path) => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (plugin && file && plugin.syncMetadata) {
        return plugin.syncMetadata.markAsChanged(file);
      }
    }, relativeFilePath);

    await waitForAsyncOperation(500);

    // Get initial changed_at
    const initialChangedAt = await getChangedAt(page, relativeFilePath);
    expect(initialChangedAt).toBeTruthy();

    // Wait a bit to ensure timestamp difference
    await waitForAsyncOperation(1000);

    // Mark as changed second time
    await page.evaluate((path) => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (plugin && file && plugin.syncMetadata) {
        return plugin.syncMetadata.markAsChanged(file);
      }
    }, relativeFilePath);

    await waitForAsyncOperation(500);

    // Check that changed_at was updated
    const updatedChangedAt = await getChangedAt(page, relativeFilePath);

    expect(updatedChangedAt).toBeTruthy();
    expect(updatedChangedAt).not.toBe(initialChangedAt);
    expect(new Date(updatedChangedAt).getTime()).toBeGreaterThan(new Date(initialChangedAt).getTime());

    console.log(`✅ changed_at updated: ${initialChangedAt} -> ${updatedChangedAt}`);
  });

  test("should persist changed_at in sync metadata storage", async () => {
    const testSlug = "test-persistence";
    const relativeFilePath = `articles/${testSlug}.md`;

    // Create initial file
    const content = `---
Title: "Test Persistence"
Slug: "${testSlug}"
Status: "draft"
---

Content for persistence test.`;

    await createTestFile(page, relativeFilePath, content);

    // Mark as changed
    await page.evaluate(({ path }) => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (plugin && file && plugin.syncMetadata) {
        return plugin.syncMetadata.markAsChanged(file);
      }
    }, { path: relativeFilePath });

    await waitForAsyncOperation(500);

    // Get changed_at
    const changedAt = await getChangedAt(page, relativeFilePath);
    expect(changedAt).toBeTruthy();

    // Verify it's stored in plugin data (not frontmatter)
    const pluginData = await page.evaluate(() => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      return plugin.loadData();
    });

    const syncMetadata = (await pluginData)?.['sync-metadata'];
    expect(syncMetadata).toBeTruthy();
    expect(syncMetadata[relativeFilePath]).toBeTruthy();
    expect(syncMetadata[relativeFilePath].changed_at).toBe(changedAt);

    console.log(`✅ changed_at persisted in sync metadata: ${changedAt}`);
  });
});
