{"log": {"version": "1.2", "creator": {"name": "Playwright", "version": "1.0.0"}, "entries": [{"request": {"method": "POST", "url": "https://solnic.ghost.io/ghost/api/admin/posts/", "headers": [{"name": "Authorization", "value": "Ghost [token]"}, {"name": "Content-Type", "value": "application/json"}], "postData": {"mimeType": "application/json", "text": "{\"posts\":[{\"title\":\"E2E Round-trip Test\",\"slug\":\"e2e-roundtrip-test\",\"status\":\"draft\",\"html\":\"<h1>E2E Round-trip Test</h1><p>This is a test post for round-trip sync testing.</p><p><strong>Updated at:</strong> 2025-08-16T13:13:28.423Z</p><p>Content that should sync from Obsidian to Ghost and back.</p><h2>Test Section</h2><ul><li>Item 1</li><li>Item 2</li><li>Item 3</li></ul><p>This content should be preserved during the round-trip sync.</p>\"}]}"}}, "response": {"status": 201, "statusText": "Created", "headers": [{"name": "Content-Type", "value": "application/json"}], "content": {"mimeType": "application/json", "text": "{\"posts\":[{\"id\":\"roundtrip-test-id\",\"title\":\"E2E Round-trip Test\",\"slug\":\"e2e-roundtrip-test\",\"status\":\"draft\",\"featured\":false,\"created_at\":\"2025-08-16T13:13:28.423Z\",\"updated_at\":\"2025-08-16T13:13:28.423Z\",\"html\":\"<h1>E2E Round-trip Test</h1><p>This is a test post for round-trip sync testing.</p><p><strong>Updated at:</strong> 2025-08-16T13:13:28.423Z</p><p>Content that should sync from Obsidian to Ghost and back.</p><h2>Test Section</h2><ul><li>Item 1</li><li>Item 2</li><li>Item 3</li></ul><p>This content should be preserved during the round-trip sync.</p>\",\"plaintext\":\"E2E Round-trip Test\\n\\nThis is a test post for round-trip sync testing.\\n\\nUpdated at: 2025-08-16T13:13:28.423Z\\n\\nContent that should sync from Obsidian to Ghost and back.\\n\\nTest Section\\n\\n* Item 1\\n* Item 2\\n* Item 3\\n\\nThis content should be preserved during the round-trip sync.\",\"tags\":[],\"authors\":[{\"id\":\"1\",\"name\":\"Test Author\"}]}]}"}}}, {"request": {"method": "PUT", "url": "https://solnic.ghost.io/ghost/api/admin/posts/roundtrip-test-id/", "headers": [{"name": "Authorization", "value": "Ghost [token]"}, {"name": "Content-Type", "value": "application/json"}], "postData": {"mimeType": "application/json", "text": "{\"posts\":[{\"id\":\"roundtrip-test-id\",\"title\":\"E2E Round-trip Test\",\"slug\":\"e2e-roundtrip-test\",\"status\":\"draft\",\"html\":\"<h1>E2E Round-trip Test</h1><p>This is a test post for round-trip sync testing.</p><p><strong>Updated at:</strong> 2025-08-16T13:13:28.423Z</p><p>Content that should sync from Obsidian to Ghost and back.</p><h2>Test Section</h2><ul><li>Item 1</li><li>Item 2</li><li>Item 3</li></ul><p>This content should be preserved during the round-trip sync.</p>\",\"updated_at\":\"2025-08-16T13:13:28.423Z\"}]}"}}, "response": {"status": 200, "statusText": "OK", "headers": [{"name": "Content-Type", "value": "application/json"}], "content": {"mimeType": "application/json", "text": "{\"posts\":[{\"id\":\"roundtrip-test-id\",\"title\":\"E2E Round-trip Test\",\"slug\":\"e2e-roundtrip-test\",\"status\":\"draft\",\"featured\":false,\"created_at\":\"2025-08-16T13:13:28.423Z\",\"updated_at\":\"2025-08-16T13:13:35.004Z\",\"html\":\"<h1>E2E Round-trip Test</h1><p>This is a test post for round-trip sync testing.</p><p><strong>Updated at:</strong> 2025-08-16T13:13:28.423Z</p><p>Content that should sync from Obsidian to Ghost and back.</p><h2>Test Section</h2><ul><li>Item 1</li><li>Item 2</li><li>Item 3</li></ul><p>This content should be preserved during the round-trip sync.</p>\",\"plaintext\":\"E2E Round-trip Test\\n\\nThis is a test post for round-trip sync testing.\\n\\nUpdated at: 2025-08-16T13:13:28.423Z\\n\\nContent that should sync from Obsidian to Ghost and back.\\n\\nTest Section\\n\\n* Item 1\\n* Item 2\\n* Item 3\\n\\nThis content should be preserved during the round-trip sync.\",\"tags\":[],\"authors\":[{\"id\":\"1\",\"name\":\"Test Author\"}]}]}"}}}, {"request": {"method": "GET", "url": "https://solnic.ghost.io/ghost/api/admin/posts/?filter=title:'E2E Round-trip Test'&include=tags,authors", "headers": [{"name": "Authorization", "value": "Ghost [token]"}, {"name": "Content-Type", "value": "application/json"}]}, "response": {"status": 200, "statusText": "OK", "headers": [{"name": "Content-Type", "value": "application/json"}], "content": {"mimeType": "application/json", "text": "{\"posts\":[{\"id\":\"roundtrip-test-id\",\"title\":\"E2E Round-trip Test\",\"slug\":\"e2e-roundtrip-test\",\"status\":\"draft\",\"featured\":false,\"created_at\":\"2025-08-16T13:13:28.423Z\",\"updated_at\":\"2025-08-16T13:13:35.004Z\",\"html\":\"<h1>E2E Round-trip Test</h1><p>This is a test post for round-trip sync testing.</p><p><strong>Updated at:</strong> 2025-08-16T13:13:28.423Z</p><p><strong>Modified in Ghost:</strong> 2025-08-16T13:13:35.004Z</p><p>Content that was updated in Ghost and should sync back to Obsidian.</p><h2>Test Section</h2><ul><li>Item 1</li><li>Item 2</li><li>Item 3</li></ul><p>This content should be preserved during the round-trip sync.</p>\",\"plaintext\":\"E2E Round-trip Test\\n\\nThis is a test post for round-trip sync testing.\\n\\nUpdated at: 2025-08-16T13:13:28.423Z\\nModified in Ghost: 2025-08-16T13:13:35.004Z\\n\\nContent that was updated in Ghost and should sync back to Obsidian.\\n\\nTest Section\\n\\n* Item 1\\n* Item 2\\n* Item 3\\n\\nThis content should be preserved during the round-trip sync.\",\"tags\":[],\"authors\":[{\"id\":\"1\",\"name\":\"Test Author\"}]}],\"meta\":{\"pagination\":{\"page\":1,\"limit\":15,\"pages\":1,\"total\":1,\"next\":null,\"prev\":null}}}"}}}]}}