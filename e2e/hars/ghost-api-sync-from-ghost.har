{"log": {"version": "1.2", "creator": {"name": "Playwright", "version": "1.0.0"}, "entries": [{"request": {"method": "GET", "url": "https://solnic.ghost.io/ghost/api/admin/posts/?limit=all&include=tags,authors", "headers": [{"name": "Authorization", "value": "Ghost [token]"}, {"name": "Content-Type", "value": "application/json"}]}, "response": {"status": 200, "statusText": "OK", "headers": [{"name": "Content-Type", "value": "application/json"}], "content": {"mimeType": "application/json", "text": "{\"posts\":[{\"id\":\"1\",\"title\":\"E2E Sync Test\",\"slug\":\"e2e-sync-from-ghost-test\",\"status\":\"draft\",\"featured\":false,\"created_at\":\"2024-01-01T10:00:00.000Z\",\"updated_at\":\"2024-01-01T12:00:00.000Z\",\"html\":\"<h1>E2E Sync Test</h1><p>This is the updated content from Ghost that should replace the local version.</p><p><PERSON> has newer changes that should be synced to local.</p>\",\"plaintext\":\"E2E Sync Test\\n\\nThis is the updated content from <PERSON> that should replace the local version.\\n\\nGhost has newer changes that should be synced to local.\",\"tags\":[],\"authors\":[{\"id\":\"1\",\"name\":\"Test Author\"}]},{\"id\":\"2\",\"title\":\"Another Test Post\",\"slug\":\"another-test-post\",\"status\":\"draft\",\"featured\":false,\"created_at\":\"2024-01-01T09:00:00.000Z\",\"updated_at\":\"2024-01-01T11:00:00.000Z\",\"html\":\"<h1>Another Test Post</h1><p>This is another test post for sync testing.</p>\",\"plaintext\":\"Another Test Post\\n\\nThis is another test post for sync testing.\",\"tags\":[],\"authors\":[{\"id\":\"1\",\"name\":\"Test Author\"}]}],\"meta\":{\"pagination\":{\"page\":1,\"limit\":\"all\",\"pages\":1,\"total\":2,\"next\":null,\"prev\":null}}}"}}}, {"request": {"method": "GET", "url": "https://solnic.ghost.io/ghost/api/admin/posts/slug/e2e-sync-test-ghost-version/", "headers": [{"name": "Authorization", "value": "Ghost [token]"}, {"name": "Content-Type", "value": "application/json"}]}, "response": {"status": 200, "statusText": "OK", "headers": [{"name": "Content-Type", "value": "application/json"}], "content": {"mimeType": "application/json", "text": "{\"posts\":[{\"id\":\"1\",\"title\":\"E2E Sync Test\",\"slug\":\"e2e-sync-from-ghost-test\",\"status\":\"draft\",\"featured\":false,\"created_at\":\"2024-01-01T10:00:00.000Z\",\"updated_at\":\"2024-01-01T12:00:00.000Z\",\"html\":\"<h1>E2E Sync Test</h1><p>This is the updated content from Ghost that should replace the local version.</p><p><PERSON> has newer changes that should be synced to local.</p>\",\"plaintext\":\"E2E Sync Test\\n\\nThis is the updated content from <PERSON> that should replace the local version.\\n\\nGhost has newer changes that should be synced to local.\",\"tags\":[],\"authors\":[{\"id\":\"1\",\"name\":\"Test Author\"}]}]}"}}}]}}