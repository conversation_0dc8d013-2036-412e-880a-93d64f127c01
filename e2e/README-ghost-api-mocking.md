# Ghost API Mocking for E2E Tests

This guide explains how to use Ghost API mocking in e2e tests using <PERSON><PERSON>'s HAR (HTTP Archive) recording and replaying functionality. This is the TypeScript equivalent of Ruby's VCR gem.

## Overview

The Ghost API mocking system allows you to:
- **Record** real API interactions with Ghost CMS
- **Replay** those interactions in tests for deterministic, fast testing
- **Update** recordings when the API changes
- **Isolate** tests from network dependencies

## Quick Start

### 1. Set up mocking in your test

```typescript
import { setupGhostAPIMocking, shouldRecordGhostAPI } from '../fixtures/ghost-api-helper';

describe("Your Test Suite", () => {
  let browser: Browser;
  let page: Page;

  beforeAll(async () => {
    // Connect to Obsidian
    browser = await chromium.connectOverCDP('http://127.0.0.1:9222');
    const contexts = browser.contexts();
    const context = contexts[0];
    page = contexts[0].pages()[0];

    // Set up Ghost API mocking
    await setupGhostAPIMocking(page, context, {
      ghostUrl: 'https://your-ghost-site.com',
      scenario: 'your-test-scenario', // Unique name for this test scenario
      record: shouldRecordGhostAPI()
    });

    // Continue with your test setup...
  });
});
```

### 2. Recording New Interactions

To record new API interactions:

```bash
# Set environment variable to enable recording
GHOST_API_RECORD=true npm run test:e2e

# Or for a specific test
GHOST_API_RECORD=true npm run test:e2e -- create-new-post.e2e.ts
```

This will:
- Make real API calls to Ghost
- Record all HTTP interactions in HAR files
- Save them to `e2e/hars/ghost-api-{scenario}.har`

### 3. Replaying Interactions (Normal Testing)

For normal test runs (default behavior):

```bash
npm run test:e2e
```

This will:
- Use recorded HAR files instead of making real API calls
- Run tests faster and more reliably
- Work offline

## File Structure

```
e2e/
├── fixtures/
│   └── ghost-api-helper.ts     # Helper utilities
├── hars/                       # HAR files (commit these!)
│   ├── ghost-api-create-post.har
│   ├── ghost-api-sync-posts.har
│   └── ghost-api-browse-posts.har
└── specs/
    └── your-test.e2e.ts
```

## Best Practices

### 1. Scenario Naming

Use descriptive scenario names that match your test functionality:

```typescript
// Good
scenario: 'create-new-post'
scenario: 'sync-existing-post'
scenario: 'browse-all-posts'

// Bad
scenario: 'test1'
scenario: 'api-test'
```

### 2. Recording Strategy

- **Record once** per test scenario when setting up tests
- **Re-record** when Ghost API changes or test requirements change
- **Commit HAR files** to version control
- **Review HAR files** before committing to ensure no sensitive data

### 3. Test Data Management

When recording, use consistent test data:

```typescript
// Use predictable test data
const testPost = {
  title: "E2E Test Post",
  slug: "e2e-test-post",
  content: "This is a test post for e2e testing"
};
```

### 4. Environment Configuration

Set up different Ghost URLs for different environments:

```typescript
const ghostUrl = process.env.GHOST_URL || 'https://demo.ghost.io';

await setupGhostAPIMocking(page, context, {
  ghostUrl,
  scenario: 'your-scenario',
  record: shouldRecordGhostAPI()
});
```

## Updating Recordings

When you need to update recordings (e.g., API changes):

1. **Delete old HAR file**:
   ```bash
   rm e2e/hars/ghost-api-your-scenario.har
   ```

2. **Record new interactions**:
   ```bash
   GHOST_API_RECORD=true npm run test:e2e -- your-test.e2e.ts
   ```

3. **Verify and commit**:
   ```bash
   git add e2e/hars/
   git commit -m "Update Ghost API recordings for scenario: your-scenario"
   ```

## Troubleshooting

### HAR File Not Found

If you get "HAR file not found" errors:
1. Run with `GHOST_API_RECORD=true` to create the HAR file
2. Ensure the scenario name matches exactly
3. Check file permissions

### API Calls Still Going Through

If tests are making real API calls instead of using mocks:
1. Verify the Ghost URL pattern matches your actual API calls
2. Check that `record: false` (or omitted) in your test setup
3. Ensure HAR file exists and is valid

### Sensitive Data in HAR Files

HAR files may contain sensitive data. Before committing:
1. Review HAR files for API keys, tokens, or personal data
2. Edit HAR files manually if needed to remove sensitive data
3. Consider using environment-specific test accounts

## Advanced Usage

### Custom Ghost URL Patterns

```typescript
// For custom Ghost installations
await setupGhostAPIMocking(page, context, {
  ghostUrl: 'https://my-custom-ghost.com',
  scenario: 'custom-scenario'
});
```

### Multiple Scenarios in One Test

```typescript
// Different scenarios for different test phases
beforeEach(async () => {
  if (testName.includes('create')) {
    await setupGhostAPIMocking(page, context, {
      ghostUrl: 'https://demo.ghost.io',
      scenario: 'create-post'
    });
  } else if (testName.includes('update')) {
    await setupGhostAPIMocking(page, context, {
      ghostUrl: 'https://demo.ghost.io',
      scenario: 'update-post'
    });
  }
});
```

## Integration with CI/CD

In your CI pipeline, always run tests in replay mode:

```yaml
# GitHub Actions example
- name: Run E2E Tests
  run: npm run test:e2e
  env:
    GHOST_API_RECORD: false  # Ensure we don't record in CI
```

Only record locally when updating test scenarios.
