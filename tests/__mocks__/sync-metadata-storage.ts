import { vi } from 'vitest';
import type { TFile } from 'obsidian';
import type { SyncMetadataStorage } from '../../src/services/sync-metadata-storage';

export function createMockSyncMetadataStorage(): SyncMetadataStorage {
  // Use a shared metadata store that persists across calls
  const metadata = new Map<string, { changed_at?: string; synced_at?: string }>();

  const mock = {
    load: vi.fn().mockResolvedValue(undefined),
    save: vi.fn().mockResolvedValue(undefined),
    setChangedAt: vi.fn().mockImplementation(async (file: TFile, timestamp: string) => {
      const existing = metadata.get(file.path) || {};
      existing.changed_at = timestamp;
      metadata.set(file.path, existing);
    }),
    setSyncedAt: vi.fn().mockImplementation(async (file: TFile, timestamp: string) => {
      const existing = metadata.get(file.path) || {};
      existing.synced_at = timestamp;
      metadata.set(file.path, existing);
    }),
    getMetadata: vi.fn().mockImplementation((file: TFile) => {
      return metadata.get(file.path) || {};
    }),
    getChangedAt: vi.fn().mockImplementation((file: TFile) => {
      return metadata.get(file.path)?.changed_at;
    }),
    getSyncedAt: vi.fn().mockImplementation((file: TFile) => {
      return metadata.get(file.path)?.synced_at;
    }),
    clearMetadata: vi.fn().mockImplementation(async (file: TFile) => {
      metadata.delete(file.path);
    }),
    markAsChanged: vi.fn().mockImplementation(async (file: TFile) => {
      const existing = metadata.get(file.path) || {};
      existing.changed_at = new Date().toISOString();
      metadata.set(file.path, existing);
    }),
    markAsSynced: vi.fn().mockImplementation(async (file: TFile) => {
      const existing = metadata.get(file.path) || {};
      existing.synced_at = new Date().toISOString();
      metadata.set(file.path, existing);
    }),
    setSyncFromGhost: vi.fn().mockImplementation(async (file: TFile, ghostUpdatedAt: string) => {
      const syncTime = new Date().toISOString();
      metadata.set(file.path, {
        changed_at: ghostUpdatedAt,
        synced_at: syncTime
      });
    }),
    // Add method to manually set metadata for tests
    _setTestMetadata: (filePath: string, data: { changed_at?: string; synced_at?: string }) => {
      metadata.set(filePath, data);
    },
    // Add method to clear all metadata for tests
    _clearAllTestMetadata: () => {
      metadata.clear();
    }
  } as unknown as SyncMetadataStorage;

  return mock;
}
