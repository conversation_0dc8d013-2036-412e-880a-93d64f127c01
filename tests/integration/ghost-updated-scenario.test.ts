import { describe, it, expect, beforeEach, vi } from 'vitest';
import { SmartSyncService } from '../../src/services/smart-sync-service';
import { ContentConverter } from '../../src/utils/content-converter';
import { SyncDecision } from '../../src/types';
import type { TFile } from 'obsidian';
import { createMockSyncMetadataStorage } from '../__mocks__/sync-metadata-storage';

describe('Ghost Updated Scenario Integration Test', () => {
  let service: SmartSyncService;
  let mockGhostAPI: any;
  let mockFiles: Map<string, string>;
  let mockFile: TFile;
  let mockSyncMetadata: any;

  beforeEach(() => {
    mockFiles = new Map();

    mockGhostAPI = {
      getPostBySlug: vi.fn(),
      createPost: vi.fn(),
      updatePost: vi.fn()
    };

    const mockReadFile = vi.fn().mockImplementation((file: TFile) => {
      const content = mockFiles.get(file.path);
      if (!content) throw new Error(`File not found: ${file.path}`);
      return Promise.resolve(content);
    });

    const mockWriteFile = vi.fn().mockImplementation((file: TFile, content: string) => {
      mockFiles.set(file.path, content);
      return Promise.resolve();
    });

    mockSyncMetadata = createMockSyncMetadataStorage();

    service = new SmartSyncService({
      ghostAPI: mockGhostAPI,
      readFile: mockReadFile,
      writeFile: mockWriteFile,
      parseMarkdown: (content: string) => {
        const parsed = ContentConverter.parseMarkdown(content);
        return { frontMatter: parsed.frontMatter, content: parsed.markdownContent };
      },
      syncMetadata: mockSyncMetadata
    });

    mockFile = {
      path: 'articles/test-post.md',
      stat: {
        mtime: new Date('2024-01-01T12:00:00.000Z').getTime(),
        ctime: new Date('2024-01-01T10:00:00.000Z').getTime(),
        size: 1000
      }
    } as TFile;
  });

  it('should sync from Ghost when Ghost is updated but local file is unchanged', async () => {
    // Scenario:
    // 1. Post is fully synced
    // 2. User updates post in Ghost
    // 3. User clicks "Sync" in Obsidian
    // Expected: Should automatically sync from Ghost without showing conflict dialog

    // Setup: Local file that was previously synced
    const localContent = `---
title: Original Title
slug: test-post
status: draft
---

# Original Title

Original content here.`;

    mockFiles.set(mockFile.path, localContent);

    // Set up metadata storage with sync timestamps
    mockSyncMetadata._setTestMetadata(mockFile.path, {
      synced_at: '2024-01-01T10:00:00.000Z',
      changed_at: '2024-01-01T09:30:00.000Z'
    });

    // File was not modified since sync (mtime is older than synced_at)
    mockFile.stat!.mtime = new Date('2024-01-01T09:45:00.000Z').getTime();

    // Ghost post has been updated since sync
    const ghostPost = {
      id: '1',
      title: 'Updated Ghost Title',
      slug: 'test-post',
      status: 'draft' as const,
      featured: false,
      created_at: '2024-01-01T09:00:00.000Z',
      updated_at: '2024-01-01T11:00:00.000Z', // Newer than synced_at
      html: '<h1>Updated Ghost Title</h1><p>Updated content from Ghost</p>',
      tags: [] as any[]
    };

    const localPost = await service.parseLocalPost(mockFile);
    const analysis = await service.analyzeSyncNeeded(localPost, ghostPost);

    // Should decide to sync FROM Ghost (not show conflict)
    expect(analysis.decision).toBe(SyncDecision.SYNC_FROM_GHOST);
    expect(analysis.reason).toBe('Ghost has newer changes');

    // Verify the timestamps that led to this decision
    expect(analysis.lastSyncTime).toBe('2024-01-01T10:00:00.000Z');
    expect(analysis.ghostUpdatedTime).toBe('2024-01-01T11:00:00.000Z');
    expect(analysis.localChangedTime).toBe('2024-01-01T09:30:00.000Z');

    // Execute the sync
    await service.syncFromGhost(mockFile, ghostPost);

    // Verify the file was updated with Ghost content
    const updatedContent = mockFiles.get(mockFile.path);
    expect(updatedContent).toContain('Title: "Updated Ghost Title"');
    expect(updatedContent).toContain('# Updated Ghost Title');
    expect(updatedContent).toContain('Updated content from Ghost');
    // NOTE: Synced At is no longer in frontmatter - it's stored in SyncMetadataStorage
    expect(updatedContent).not.toContain('Synced At:');
  });

  it('should show conflict when both Ghost and local have changes since last sync', async () => {
    // Scenario:
    // 1. Post is synced
    // 2. User updates post in Ghost
    // 3. User also updates post locally
    // 4. User clicks "Sync" in Obsidian
    // Expected: Should show conflict dialog

    // Setup: Local file with changes since sync
    const localContent = `---
title: Local Changes
slug: test-post
status: draft
---

# Local Changes

Content updated locally.`;

    mockFiles.set(mockFile.path, localContent);

    // Set up metadata storage with sync timestamps
    mockSyncMetadata._setTestMetadata(mockFile.path, {
      synced_at: '2024-01-01T10:00:00.000Z',
      changed_at: '2024-01-01T11:30:00.000Z'
    });

    // File was modified after sync
    mockFile.stat!.mtime = new Date('2024-01-01T11:30:00.000Z').getTime();

    // Ghost post has also been updated since sync
    const ghostPost = {
      id: '1',
      title: 'Ghost Changes',
      slug: 'test-post',
      status: 'draft' as const,
      featured: false,
      created_at: '2024-01-01T09:00:00.000Z',
      updated_at: '2024-01-01T11:00:00.000Z', // Also newer than synced_at
      html: '<h1>Ghost Changes</h1><p>Content updated in Ghost</p>'
    };

    const localPost = await service.parseLocalPost(mockFile);
    const analysis = await service.analyzeSyncNeeded(localPost, ghostPost);

    // Should detect conflict
    expect(analysis.decision).toBe(SyncDecision.CONFLICT);
    expect(analysis.reason).toBe('Both Ghost and local content have changes since last sync');
  });

  it('should indicate no sync needed when nothing has changed', async () => {
    // Scenario:
    // 1. Post is synced
    // 2. No changes in Ghost or locally
    // 3. User clicks "Sync" in Obsidian
    // Expected: Should indicate no sync needed

    // Setup: Local file with no changes since sync
    const localContent = `---
title: Unchanged Post
slug: test-post
status: draft
---

# Unchanged Post

No changes here.`;

    mockFiles.set(mockFile.path, localContent);

    // Set up metadata storage with sync timestamps
    mockSyncMetadata._setTestMetadata(mockFile.path, {
      synced_at: '2024-01-01T10:00:00.000Z',
      changed_at: '2024-01-01T09:30:00.000Z'
    });

    // File was not modified since sync
    mockFile.stat!.mtime = new Date('2024-01-01T09:45:00.000Z').getTime();

    // Ghost post has also not been updated since sync
    const ghostPost = {
      id: '1',
      title: 'Unchanged Post',
      slug: 'test-post',
      status: 'draft' as const,
      featured: false,
      created_at: '2024-01-01T09:00:00.000Z',
      updated_at: '2024-01-01T09:45:00.000Z', // Older than synced_at
      html: '<h1>Unchanged Post</h1><p>No changes here</p>'
    };

    const localPost = await service.parseLocalPost(mockFile);
    const analysis = await service.analyzeSyncNeeded(localPost, ghostPost);

    // Should indicate no sync needed
    expect(analysis.decision).toBe(SyncDecision.NO_SYNC_NEEDED);
    expect(analysis.reason).toBe('No changes detected since last sync');
  });
});
