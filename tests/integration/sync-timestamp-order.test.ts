/**
 * Test to verify that synced_at is NOT set before changed_at when syncing from Ghost
 * This test reproduces the bug where synced_at was being set before changed_at,
 * causing incorrect conflict detection.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { SmartSyncService } from '../../src/services/smart-sync-service';
import { SyncMetadataStorage } from '../../src/services/sync-metadata-storage';
import type { GhostPost } from '../../src/types';
import type { TFile } from 'obsidian';

describe('Sync Timestamp Order Bug Fix', () => {
  let smartSyncService: SmartSyncService;
  let mockSyncMetadata: SyncMetadataStorage;
  let mockFile: TFile;
  let timestampOrder: string[] = [];

  beforeEach(() => {
    timestampOrder = [];

    // Mock SyncMetadataStorage to track the order of timestamp setting
    mockSyncMetadata = {
      setSyncFromGhost: vi.fn().mockImplementation(async (file: TFile, ghostUpdatedAt: string) => {
        const syncTime = new Date().toISOString();

        // Track the order of timestamp setting
        timestampOrder.push(`changed_at=${ghostUpdatedAt}`);
        timestampOrder.push(`synced_at=${syncTime}`);

        console.log('setSyncFromGhost called:');
        console.log(`  changed_at set to: ${ghostUpdatedAt}`);
        console.log(`  synced_at set to: ${syncTime}`);
        console.log(`  Order: ${timestampOrder.join(' -> ')}`);
      }),
      setChangedAt: vi.fn().mockImplementation(async (file: TFile, timestamp: string) => {
        timestampOrder.push(`changed_at=${timestamp}`);
        console.log(`setChangedAt called: ${timestamp}`);
      }),
      setSyncedAt: vi.fn().mockImplementation(async (file: TFile, timestamp: string) => {
        timestampOrder.push(`synced_at=${timestamp}`);
        console.log(`setSyncedAt called: ${timestamp}`);
      }),
      markAsChanged: vi.fn().mockImplementation(async (file: TFile) => {
        const timestamp = new Date().toISOString();
        timestampOrder.push(`changed_at=${timestamp} (markAsChanged)`);
        console.log(`markAsChanged called: ${timestamp}`);
      }),
      markAsSynced: vi.fn().mockImplementation(async (file: TFile) => {
        const timestamp = new Date().toISOString();
        timestampOrder.push(`synced_at=${timestamp} (markAsSynced)`);
        console.log(`markAsSynced called: ${timestamp}`);
      })
    } as any;

    smartSyncService = new SmartSyncService({
      ghostAPI: {} as any,
      readFile: vi.fn(),
      writeFile: vi.fn(),
      parseMarkdown: vi.fn(),
      syncMetadata: mockSyncMetadata
    });

    mockFile = {
      path: 'test-post.md',
      name: 'test-post.md'
    } as TFile;
  });

  it('should set changed_at before synced_at when syncing from Ghost', async () => {
    const ghostPost: GhostPost = {
      id: '1',
      title: 'Test Post',
      slug: 'test-post',
      status: 'draft',
      featured: false,
      created_at: '2024-01-01T10:00:00.000Z',
      updated_at: '2024-01-01T11:00:00.000Z', // Ghost was updated at 11:00
      html: '<p>Test content</p>'
    };

    // Sync from Ghost
    await smartSyncService.syncFromGhost(mockFile, ghostPost);

    // Verify that setSyncFromGhost was called (which sets both timestamps correctly)
    expect(mockSyncMetadata.setSyncFromGhost).toHaveBeenCalledWith(mockFile, ghostPost.updated_at);

    // Verify the order: changed_at should be set before synced_at
    expect(timestampOrder).toHaveLength(2);
    expect(timestampOrder[0]).toMatch(/^changed_at=2024-01-01T11:00:00\.000Z$/);
    expect(timestampOrder[1]).toMatch(/^synced_at=\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/);

    console.log('✅ Timestamp order is correct:', timestampOrder);
  });

  it('should NOT call markAsChanged or markAsSynced separately during syncFromGhost', async () => {
    const ghostPost: GhostPost = {
      id: '1',
      title: 'Test Post',
      slug: 'test-post',
      status: 'draft',
      featured: false,
      created_at: '2024-01-01T10:00:00.000Z',
      updated_at: '2024-01-01T11:00:00.000Z',
      html: '<p>Test content</p>'
    };

    // Sync from Ghost
    await smartSyncService.syncFromGhost(mockFile, ghostPost);

    // Verify that individual timestamp methods were NOT called
    expect(mockSyncMetadata.setChangedAt).not.toHaveBeenCalled();
    expect(mockSyncMetadata.setSyncedAt).not.toHaveBeenCalled();
    expect(mockSyncMetadata.markAsChanged).not.toHaveBeenCalled();
    expect(mockSyncMetadata.markAsSynced).not.toHaveBeenCalled();

    // Only setSyncFromGhost should have been called
    expect(mockSyncMetadata.setSyncFromGhost).toHaveBeenCalledOnce();

    console.log('✅ Only setSyncFromGhost was called, no individual timestamp methods');
  });

  it('should set changed_at to Ghost updated_at, not current time', async () => {
    const ghostUpdatedAt = '2024-01-01T11:00:00.000Z';
    const ghostPost: GhostPost = {
      id: '1',
      title: 'Test Post',
      slug: 'test-post',
      status: 'draft',
      featured: false,
      created_at: '2024-01-01T10:00:00.000Z',
      updated_at: ghostUpdatedAt,
      html: '<p>Test content</p>'
    };

    // Sync from Ghost
    await smartSyncService.syncFromGhost(mockFile, ghostPost);

    // Verify that changed_at is set to Ghost's updated_at, not current time
    expect(timestampOrder[0]).toBe(`changed_at=${ghostUpdatedAt}`);

    // Verify that synced_at is set to current time (should be different from Ghost updated_at)
    expect(timestampOrder[1]).toMatch(/^synced_at=\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/);
    expect(timestampOrder[1]).not.toBe(`synced_at=${ghostUpdatedAt}`);

    console.log('✅ changed_at set to Ghost updated_at, synced_at set to current time');
  });

  it('should update synced_at after syncing TO Ghost', async () => {
    const localPost = {
      frontMatter: {
        title: 'Test Post',
        slug: 'test-post',
        status: 'draft' as const
      },
      content: 'Test content',
      filePath: 'test-post.md',
      syncedAt: undefined as string | undefined,
      changedAt: undefined as string | undefined,
      fileModifiedAt: Date.now()
    };

    const mockGhostAPI = {
      getPostBySlug: vi.fn().mockResolvedValue(null), // No existing post
      createPost: vi.fn().mockResolvedValue({
        id: '1',
        title: 'Test Post',
        slug: 'test-post',
        updated_at: '2024-01-01T12:00:00.000Z' // Ghost's updated_at after creation
      })
    };

    const smartSyncService = new SmartSyncService({
      ghostAPI: mockGhostAPI as any,
      readFile: vi.fn(),
      writeFile: vi.fn(),
      parseMarkdown: vi.fn(),
      syncMetadata: mockSyncMetadata
    });

    // Sync TO Ghost
    const result = await smartSyncService.syncToGhost(localPost);

    // Verify that the result contains the updated_at from Ghost
    expect(result.updated_at).toBe('2024-01-01T12:00:00.000Z');

    // Note: The actual setSyncedAt call should be made by the calling code (view)
    // with the result.updated_at value. This test verifies that the result
    // contains the correct updated_at that should be used for setSyncedAt.

    console.log('✅ syncToGhost returns Ghost updated_at for timestamp update');
  });
});
