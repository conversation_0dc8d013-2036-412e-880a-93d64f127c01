import { describe, it, expect, beforeEach, vi } from 'vitest';
import { SmartSyncService } from '../../src/services/smart-sync-service';
import { ContentConverter } from '../../src/utils/content-converter';
import type { TFile } from 'obsidian';
import { createMockSyncMetadataStorage } from '../__mocks__/sync-metadata-storage';

/**
 * Critical test to ensure the "Content is being updated" placeholder
 * is NEVER set during any sync operation
 */
describe('No Placeholder Content Integration Test', () => {
  let service: SmartSyncService;
  let mockGhostAPI: any;
  let mockFiles: Map<string, string>;
  let mockFile: TFile;

  beforeEach(() => {
    mockFiles = new Map();

    mockGhostAPI = {
      getPostBySlug: vi.fn(),
      createPost: vi.fn(),
      updatePost: vi.fn()
    };

    const mockReadFile = vi.fn().mockImplementation((file: TFile) => {
      const content = mockFiles.get(file.path);
      if (!content) throw new Error(`File not found: ${file.path}`);
      return Promise.resolve(content);
    });

    const mockWriteFile = vi.fn().mockImplementation((file: TFile, content: string) => {
      mockFiles.set(file.path, content);
      return Promise.resolve();
    });

    service = new SmartSyncService({
      ghostAPI: mockGhostAPI,
      readFile: mockReadFile,
      writeFile: mockWriteFile,
      parseMarkdown: (content: string) => {
        const parsed = ContentConverter.parseMarkdown(content);
        return { frontMatter: parsed.frontMatter, content: parsed.markdownContent };
      },
      syncMetadata: createMockSyncMetadataStorage()
    });

    mockFile = {
      path: 'articles/test-post.md',
      stat: {
        mtime: new Date('2024-01-01T12:00:00.000Z').getTime(),
        ctime: new Date('2024-01-01T10:00:00.000Z').getTime(),
        size: 1000
      }
    } as TFile;
  });

  it('should NEVER set "Content is being updated" placeholder in any scenario', async () => {
    const testScenarios = [
      {
        name: 'Empty content',
        content: '',
        expectedHtml: '<p></p>'
      },
      {
        name: 'Whitespace only content',
        content: '   \n  \t  ',
        expectedHtml: '<p></p>'
      },
      {
        name: 'Single space content',
        content: ' ',
        expectedHtml: '<p></p>'
      },
      {
        name: 'Newlines only content',
        content: '\n\n\n',
        expectedHtml: '<p></p>'
      },
      {
        name: 'Real content',
        content: '# Test\n\nReal content here',
        expectedHtmlContains: 'Real content here'
      }
    ];

    for (const scenario of testScenarios) {
      console.log(`Testing scenario: ${scenario.name}`);

      // Setup local file with the test content
      const localContent = `---
title: Test Post ${scenario.name}
slug: test-post-${scenario.name.toLowerCase().replace(/\s+/g, '-')}
---

${scenario.content}`;

      mockFiles.set(mockFile.path, localContent);

      // Mock Ghost API responses
      mockGhostAPI.getPostBySlug.mockResolvedValue(null); // Post doesn't exist
      mockGhostAPI.createPost.mockResolvedValue({
        id: '1',
        title: `Test Post ${scenario.name}`,
        slug: `test-post-${scenario.name.toLowerCase().replace(/\s+/g, '-')}`,
        status: 'draft',
        featured: false,
        created_at: '2024-01-01T12:00:00.000Z',
        updated_at: '2024-01-01T12:00:00.000Z'
      });

      // Parse and sync
      const localPost = await service.parseLocalPost(mockFile);
      await service.syncToGhost(localPost);

      // Verify the API call
      const createCall = mockGhostAPI.createPost.mock.calls[mockGhostAPI.createPost.mock.calls.length - 1];
      const postData = createCall[0];

      // CRITICAL: Ensure no placeholder text
      expect(postData.html).not.toContain('Content is being updated');
      expect(postData.html).not.toContain('being updated');
      expect(postData.html).not.toContain('updating');

      // Verify expected HTML
      if (scenario.expectedHtml) {
        expect(postData.html).toBe(scenario.expectedHtml);
      } else if (scenario.expectedHtmlContains) {
        expect(postData.html).toContain(scenario.expectedHtmlContains);
      }

      console.log(`✅ ${scenario.name}: HTML = "${postData.html}"`);
    }
  });

  it('should handle sync from Ghost without placeholder content', async () => {
    const ghostPost = {
      id: '1',
      title: 'Ghost Post',
      slug: 'ghost-post',
      status: 'draft' as const,
      featured: false,
      created_at: '2024-01-01T10:00:00.000Z',
      updated_at: '2024-01-01T11:00:00.000Z',
      html: '', // Empty HTML from Ghost
      tags: [] as any[]
    };

    // Sync from Ghost
    await service.syncFromGhost(mockFile, ghostPost);

    const updatedContent = mockFiles.get(mockFile.path);

    // Ensure no placeholder content in the synced file
    expect(updatedContent).not.toContain('Content is being updated');
    expect(updatedContent).not.toContain('being updated');
    expect(updatedContent).not.toContain('updating');

    // Should contain proper frontmatter
    expect(updatedContent).toContain('Title: "Ghost Post"');
    expect(updatedContent).toContain('Slug: "ghost-post"');
    // NOTE: Synced At is no longer in frontmatter - it's stored in SyncMetadataStorage
    expect(updatedContent).not.toContain('Synced At:');
  });

  it('should handle content conversion edge cases without placeholder', async () => {
    const edgeCases = [
      { markdown: '', description: 'empty markdown' },
      { markdown: '<!-- comment only -->', description: 'comment only' },
      { markdown: '---\n---', description: 'empty frontmatter only' },
      { markdown: '\n\n\n', description: 'newlines only' },
      { markdown: '   ', description: 'spaces only' }
    ];

    for (const edgeCase of edgeCases) {
      console.log(`Testing edge case: ${edgeCase.description}`);

      const frontMatter = {
        title: `Edge Case ${edgeCase.description}`,
        slug: `edge-case-${edgeCase.description.replace(/\s+/g, '-')}`
      };

      const result = ContentConverter.createGhostPostData(frontMatter, edgeCase.markdown);

      // CRITICAL: No placeholder content
      expect(result.html).not.toContain('Content is being updated');
      expect(result.html).not.toContain('being updated');
      expect(result.html).not.toContain('updating');

      // Should be empty paragraph for empty content
      if (!edgeCase.markdown.trim()) {
        expect(result.html).toBe('<p></p>');
      }

      console.log(`✅ ${edgeCase.description}: HTML = "${result.html}"`);
    }
  });

  it('should maintain content integrity during bidirectional sync', async () => {
    // Test a complete bidirectional sync cycle
    const originalContent = `---
title: Bidirectional Test
slug: bidirectional-test
---

# Original Content

This is the original content that should never be replaced with placeholder text.`;

    mockFiles.set(mockFile.path, originalContent);

    // Mock Ghost post
    const ghostPost = {
      id: '1',
      title: 'Bidirectional Test',
      slug: 'bidirectional-test',
      status: 'draft' as const,
      featured: false,
      created_at: '2024-01-01T10:00:00.000Z',
      updated_at: '2024-01-01T11:00:00.000Z',
      html: '<h1>Updated Content</h1><p>This content was updated in Ghost.</p>',
      tags: [] as any[]
    };

    mockGhostAPI.getPostBySlug.mockResolvedValue(ghostPost);
    mockGhostAPI.updatePost.mockResolvedValue({
      ...ghostPost,
      updated_at: '2024-01-01T12:00:00.000Z'
    });

    // Parse local post
    const localPost = await service.parseLocalPost(mockFile);

    // Sync to Ghost (local is newer due to file modification time)
    await service.syncToGhost(localPost);

    // Verify no placeholder in sync to Ghost
    const updateCall = mockGhostAPI.updatePost.mock.calls[0];
    const postData = updateCall[0];
    expect(postData.html).not.toContain('Content is being updated');
    expect(postData.html).toContain('Original Content');

    // Now sync from Ghost
    await service.syncFromGhost(mockFile, ghostPost);

    // Verify no placeholder in sync from Ghost
    const syncedContent = mockFiles.get(mockFile.path);
    expect(syncedContent).not.toContain('Content is being updated');
    expect(syncedContent).toContain('Updated Content');
    expect(syncedContent).toContain('This content was updated in Ghost');
  });
});
