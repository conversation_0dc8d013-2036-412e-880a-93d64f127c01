import { describe, it, expect, beforeEach, vi } from 'vitest';
import { SmartSyncService } from '../../src/services/smart-sync-service';
import { ContentConverter } from '../../src/utils/content-converter';
import { SyncDecision } from '../../src/types';
import type { TFile } from 'obsidian';
import type { GhostPost, LocalPost } from '../../src/types';
import { createMockSyncMetadataStorage } from '../__mocks__/sync-metadata-storage';

describe('Ghost Update No Conflict Test', () => {
  let smartSyncService: SmartSyncService;
  let mockDependencies: any;
  let mockFile: TFile;
  let mockGhostPost: GhostPost;

  beforeEach(() => {
    mockDependencies = {
      readFile: vi.fn(),
      writeFile: vi.fn(),
      parseMarkdown: vi.fn(),
      ghostAPI: {
        getPostBySlug: vi.fn(),
        updatePost: vi.fn(),
        createPost: vi.fn()
      },
      syncMetadata: createMockSyncMetadataStorage()
    };

    smartSyncService = new SmartSyncService(mockDependencies);

    mockFile = {
      path: 'test-post.md',
      name: 'test-post.md',
      basename: 'test-post',
      extension: 'md',
      stat: { mtime: Date.now() }
    } as TFile;

    mockGhostPost = {
      id: 'ghost-123',
      title: 'Updated Ghost Title',
      slug: 'test-post',
      html: '<p>Updated content from Ghost</p>',
      status: 'draft',
      updated_at: '2024-01-15T12:00:00.000Z',
      created_at: '2024-01-01T10:00:00.000Z',
      published_at: null,
      tags: [],
      primary_tag: null,
      featured: false,
      visibility: 'public'
    };
  });

  it('should NOT show conflict when Ghost is updated but local file is unchanged', async () => {
    // Scenario: User updates post in Ghost, then clicks sync without making local changes
    const baseTime = new Date('2024-01-10T10:00:00.000Z').getTime();

    // Last sync was 5 days ago
    const lastSyncTime = new Date(baseTime).toISOString();

    // Ghost was updated 1 day ago (after last sync)
    mockGhostPost.updated_at = new Date(baseTime + (4 * 24 * 60 * 60 * 1000)).toISOString();

    // Local file content with timestamps from last sync
    const localContent = `---
title: Test Post
slug: test-post
status: draft
synced_at: ${lastSyncTime}
changed_at: ${lastSyncTime}
---

Original content here.`;

    const localPost: LocalPost = {
      frontMatter: {
        title: 'Test Post',
        slug: 'test-post',
        status: 'draft'
        // NOTE: synced_at and changed_at are no longer in frontmatter - they're in SyncMetadataStorage
      },
      content: 'Original content here.',
      syncedAt: lastSyncTime,
      changedAt: lastSyncTime, // Same as synced_at - no local changes since sync
      fileModifiedAt: baseTime,
      filePath: 'test-post.md'
    };

    mockDependencies.readFile.mockResolvedValue(localContent);
    mockDependencies.parseMarkdown.mockReturnValue({
      frontMatter: localPost.frontMatter,
      content: localPost.content,
      markdownContent: localPost.content
    });

    // Analyze sync needed
    const analysis = await smartSyncService.analyzeSyncNeeded(localPost, mockGhostPost);

    // Should decide to sync FROM Ghost, NOT show conflict
    expect(analysis.decision).toBe(SyncDecision.SYNC_FROM_GHOST);
    expect(analysis.reason).toBe('Ghost has newer changes');
    expect(analysis.decision).not.toBe(SyncDecision.CONFLICT);
  });

  it('should set changed_at to Ghost updated_at when syncing from Ghost', async () => {
    // Test that when we sync FROM Ghost, changed_at is set to Ghost's updated_at
    const ghostUpdatedTime = '2024-01-15T12:00:00.000Z';
    mockGhostPost.updated_at = ghostUpdatedTime;

    // Mock the convertGhostPostToArticle to return content WITH synced_at (back to original behavior)
    const expectedContent = `---
title: Updated Ghost Title
slug: test-post
status: draft
synced_at: ${new Date().toISOString()}
changed_at: ${ghostUpdatedTime}
---

Updated content from Ghost`;

    // Mock the convertGhostPostToArticle to return content WITHOUT internal timestamps (new behavior)
    const initialContent = `---
title: Updated Ghost Title
slug: test-post
status: draft
---

Updated content from Ghost`;

    vi.spyOn(ContentConverter, 'convertGhostPostToArticle').mockReturnValue(initialContent);

    await smartSyncService.syncFromGhost(mockFile, mockGhostPost);

    expect(ContentConverter.convertGhostPostToArticle).toHaveBeenCalledWith(mockGhostPost);
    // Should be called once: only for writing content (no more timestamp updates in frontmatter)
    expect(mockDependencies.writeFile).toHaveBeenCalledTimes(1);
    expect(mockDependencies.writeFile).toHaveBeenCalledWith(mockFile, initialContent);

    // Should update metadata storage with sync timestamps
    expect(mockDependencies.syncMetadata.setSyncFromGhost).toHaveBeenCalledWith(mockFile, ghostUpdatedTime);
  });

  it('should verify ContentConverter does NOT set internal timestamps in frontmatter', () => {
    // Test the actual ContentConverter behavior
    const result = ContentConverter.convertGhostPostToArticle(mockGhostPost);

    // Parse the result to check timestamps
    const parsed = ContentConverter.parseMarkdown(result);

    // Internal timestamps should NOT be set in frontmatter anymore
    expect(parsed.frontMatter.changed_at).toBeUndefined();
    expect(parsed.frontMatter.synced_at).toBeUndefined();
    expect(parsed.frontMatter['Changed At']).toBeUndefined();
    expect(parsed.frontMatter['Synced At']).toBeUndefined();
  });
});
