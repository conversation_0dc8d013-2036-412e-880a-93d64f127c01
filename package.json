{"name": "obsidian-ghost-sync", "version": "1.0.0", "description": "Sync posts between Obsidian and Ghost.io", "main": "src/main.ts", "scripts": {"dev": "node esbuild.config.mjs", "build": "tsc -noEmit -skipLibCheck && node esbuild.config.mjs production", "test": "vitest run", "test:ci": "vitest run", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "test:ui": "vitest --ui", "test:legacy": "node test-content-conversion.js", "test:build": "npm test && npm run build", "test:e2e:setup": "npm run build && cp main.js manifest.json styles.css tests/vault/Test/.obsidian/plugins/ghost-sync/", "test:e2e:reset": "node scripts/reset-e2e-obsidian-config.js", "test:e2e:obsidian": "/Applications/Obsidian.app/Contents/MacOS/Obsidian --user-data-dir=\"$(pwd)/e2e/test_obsidian_data\" --remote-debugging-port=9222", "test:e2e:run": "npx vitest run --run e2e/", "test:e2e:changed-at": "npx vitest run --run e2e/specs/changed-at-handling.e2e.ts", "test:e2e:sync": "npx vitest run --run e2e/specs/ghost-sync-e2e.e2e.ts", "test:e2e": "npx vitest run --run e2e/", "test:e2e:record": "GHOST_API_RECORD=true npx vitest run --run e2e/", "ghost-api:list": "node scripts/manage-ghost-api-recordings.js list", "ghost-api:clean": "node scripts/manage-ghost-api-recordings.js clean", "ghost-api:info": "node scripts/manage-ghost-api-recordings.js info", "version": "node version-bump.mjs && git add manifest.json versions.json"}, "keywords": [], "author": "<PERSON>", "license": "MIT", "devDependencies": {"@sveltejs/vite-plugin-svelte": "^5.0.4", "@types/node": "^22.0.0", "@typescript-eslint/eslint-plugin": "5.29.0", "@typescript-eslint/parser": "5.29.0", "@vitest/ui": "^2.1.8", "@wdio/cli": "^9.19.1", "@wdio/local-runner": "^9.19.1", "@wdio/mocha-framework": "^9.19.1", "@wdio/selenium-standalone-service": "^8.14.0", "@wdio/spec-reporter": "^9.19.1", "builtin-modules": "3.3.0", "chai": "^5.2.1", "chromedriver": "^139.0.1", "esbuild": "0.17.3", "esbuild-svelte": "^0.9.3", "jsdom": "^26.0.0", "obsidian": "latest", "@playwright/test": "^1.48.0", "svelte": "^5.38.1", "ts-node": "^10.9.2", "tslib": "2.4.0", "typescript": "4.7.4", "vite": "^6.0.7", "vitest": "^2.1.8", "wdio-chromedriver-service": "^8.1.1"}, "dependencies": {"@tryghost/admin-api": "^1.14.0", "turndown": "^7.2.0"}}